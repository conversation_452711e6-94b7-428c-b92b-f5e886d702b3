'use client'

import React, {
  useState, useRef, useEffect,
} from 'react';
import * as fabric from 'fabric';
import { 
  CanvasSidebar, CanvasToolbar,
} from '@/common/components/organisms';
import { cn } from '@/common/utils/helpers';

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
}: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<fabric.Canvas | null>(null);

  useEffect(() => {
    if (!canvasRef.current || !isOpen) {
      return;
    }
    const canvasWidth = 800;
    const canvasHeight = 600;

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
    });

    canvas.on('mouse:wheel', function (opt) {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();
      zoom *= 0.999 ** delta;
      if (zoom > 20) {
        zoom = 20;
      }
      if (zoom < 0.01) {
        zoom = 0.01;
      }
      canvas.zoomToPoint(new fabric.Point(opt.e.offsetX, opt.e.offsetY), zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();
    });

    canvas.on('mouse:down', function (this: any, opt: any) {
      const evt = opt.e;
      if (evt.altKey === true) {
        this.isDragging = true;
        this.selection = false;
        this.lastPosX = evt.clientX;
        this.lastPosY = evt.clientY;
      }
    });

    canvas.on('mouse:move', function (this: any, opt: any) {
      if (this.isDragging) {
        const e = opt.e;
        const vpt = this.viewportTransform;
        vpt[4] += e.clientX - this.lastPosX;
        vpt[5] += e.clientY - this.lastPosY;
        this.requestRenderAll();
        this.lastPosX = e.clientX;
        this.lastPosY = e.clientY;
      }
    });

    canvas.on('mouse:up', function (this: any) {
      this.setViewportTransform(this.viewportTransform);
      this.isDragging = false;
      this.selection = true;
    });

    canvas.on('mouse:dblclick', function (opt: any) {
      const target = opt.target;
      if (target && (target.type === 'text' || target.type === 'i-text')) {
        const textObject = target as fabric.IText;
        canvas.setActiveObject(textObject);
        textObject.enterEditing();
        textObject.selectAll();
        canvas.renderAll();
      }
    });

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete') {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length) {
          activeObjects.forEach(obj => canvas.remove(obj));
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    setFabricCanvas(canvas);

    if (initialImage) {
      fabric.FabricImage.fromURL(initialImage).then((img: any) => {
        const canvasWidth = canvas.width!;
        const canvasHeight = canvas.height!;
        const imgWidth = img.width!;
        const imgHeight = img.height!;

        const scaleX = canvasWidth / imgWidth;
        const scaleY = canvasHeight / imgHeight;
        const scale = Math.min(scaleX, scaleY);

        img.scale(scale);
        img.set({
          left: (canvasWidth - img.getScaledWidth()) / 2,
          top: (canvasHeight - img.getScaledHeight()) / 2,
        });
        canvas.add(img);
        canvas.renderAll();
      });
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      canvas.dispose();
    };
  }, [isOpen, initialImage]);

  const handleSaveDesign = () => {
    if (!fabricCanvas) {
      console.error('Canvas not available');
      return;
    }

    try {
      const dataURL = fabricCanvas.toDataURL({
        format: 'png',
        quality: 1,
        multiplier: 1,
      });

      fetch(dataURL)
        .then(res => res.blob())
        .then(blob => {
          const timestamp = Date.now();
          const fileName = `canvas-design-${timestamp}.png`;
          const file = new File([blob], fileName, { type: 'image/png' });
          const tempUrl = URL.createObjectURL(file);
          onSave(tempUrl);
        })
        .catch(error => {
          console.error('Error converting canvas to file:', error);
          onSave('');
        });
    } catch (error) {
      console.error('Error generating canvas image:', error);
      onSave('');
    }
  };

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-[60px] left-0 right-0 bottom-0 h-[calc(100vh-60px)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasToolbar
        onClose={onClose}
        onSaveDesign={handleSaveDesign}
        canvas={fabricCanvas}
      />

      <div className="flex flex-1 min-h-0 flex-col md:flex-row">
        <div className="block">
          <CanvasSidebar
            canvas={fabricCanvas}
            agentId={agentId}
            planId={planId}
          />
        </div>
        <div className="flex-1 flex flex-col items-center justify-center bg-neutral-800 p-2 md:p-4">
          <div className="bg-violets-are-blue/5 rounded-xl p-2 md:p-4 w-auto">
            <canvas
              ref={canvasRef}
              className="border border-gray-200 rounded w-full max-w-full"
              style={{ maxHeight: 'calc(100vh - 200px)' }}
            />
          </div>
          <div className="mt-2 md:mt-4 text-center text-gray-500 text-xs md:text-sm px-2">
            <p className="hidden md:block">Mouse wheel to zoom | Alt + drag to pan | Double-click text to edit inline | Enter for line breaks | Delete key to remove selected objects</p>
          </div>
        </div>
      </div>
    </div>
  );
};
